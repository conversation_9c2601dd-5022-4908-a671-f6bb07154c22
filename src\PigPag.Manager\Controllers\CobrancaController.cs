using Microsoft.Extensions.DependencyInjection;
using MultiPay.Plugin.RabbitMqBase.Bus;
using MultiPay.Plugin.RabbitMqBase.Events;
using Newtonsoft.Json;
using PagedList;
using PigPag.Common;
using PigPag.Constants;
using PigPag.CreditCard;
using PigPag.CreditCard.Model;
using PigPag.CreditCard.Transaction;
using PigPag.Domain.Manager;
using PigPag.Manager.Models;
using PigPag.Manager.Models.Cobranca;
using PigPag.Resources;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;

namespace PigPag.Manager.Controllers
{
    public partial class CobrancaController : BaseAutorizacaoController
    {
        private readonly IServiceProvider serviceProvider;

        public CobrancaController(IServiceProvider serviceProvider)
        {
            this.serviceProvider = serviceProvider;
        }

        public ActionResult Pesquisar()
        {
            if (TempData["Mensagem"] != null)
                ViewBag.Mensagem = TempData["Mensagem"];
            return View();
        }

        public ActionResult _PartialPesquisar(int? pg,
                                            string codigoCobranca,
                                            string numeroFatura,
                                             byte? idFormaPagamento,
                                         string dataVencimentoInicial,
                                         string dataVencimentoFinal,
                                         string dataPagamentoInicial,
                                         string dataPagamentoFinal,
                                         string dataCriacaoInicial,
                                         string dataCriacaoFinal,
                                            string nomePagador,
                                            string emailPagador,
                                            string cpfcnpjPagador,
                                            string enderecoBitcoin,
                                            string hash,
                                            string last4,
                                            string nsu,
                                            string tid,
                                            string endtoend,
                                            string nossoNumero,
                                            string nomeCliente,
                                            string txidpix,
                                            int itensPorPagina = 25)
        {
            try
            {
                DateTime? dtVI = null, dtVF = null, dtPI = null, dtPF = null, dtCI = null, dtCF = null;
                if (!string.IsNullOrEmpty(dataVencimentoInicial))
                    dtVI = Convert.ToDateTime(dataVencimentoInicial);
                if (!string.IsNullOrEmpty(dataVencimentoFinal))
                    dtVF = Convert.ToDateTime(dataVencimentoFinal);
                if (!string.IsNullOrEmpty(dataPagamentoInicial))
                    dtPI = Convert.ToDateTime(dataPagamentoInicial);
                if (!string.IsNullOrEmpty(dataPagamentoFinal))
                    dtPF = Convert.ToDateTime(dataPagamentoFinal);
                if (!string.IsNullOrEmpty(dataCriacaoInicial))
                    dtCI = Convert.ToDateTime(dataCriacaoInicial);
                if (!string.IsNullOrEmpty(dataCriacaoFinal))
                    dtCF = Convert.ToDateTime(dataCriacaoFinal);

                if (dtVI.HasValue && !dtVF.HasValue)
                    dtVF = dtVI;
                if (dtPI.HasValue && !dtPF.HasValue)
                    dtPF = dtPI;
                if (dtCI.HasValue && !dtCF.HasValue)
                    dtCF = dtCI;

                if (!dtVI.HasValue &&
                    !dtVF.HasValue &&
                    !dtPI.HasValue &&
                    !dtPF.HasValue &&
                    !dtCI.HasValue &&
                    !dtCF.HasValue)
                {
                    TempData["Mensagem"] = new MensagemModel()
                    {
                        Tipo = MensagemModel.TipoMensagem.Alerta,
                        Mensagem = "Informe ao menos uma data para pesquisar."
                    };
                    return RedirectToRoute("partial_mensagem");
                }

                pg = pg ?? 1;
                var usuario = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();

                var dados = new Cobranca().SelectPessoaDadosBasicosPesquisar(codigoCobranca,
                                                                             numeroFatura,
                                                                             idFormaPagamento,
                                                                             dtVI,
                                                                             dtVF,
                                                                             dtPI,
                                                                             dtPF,
                                                                             dtCI,
                                                                             dtCF,
                                                                             nomePagador,
                                                                             emailPagador,
                                                                             cpfcnpjPagador,
                                                                             enderecoBitcoin,
                                                                             hash,
                                                                             last4,
                                                                             nsu,
                                                                             tid,
                                                                             endtoend,
                                                                             nossoNumero,
                                                                             nomeCliente,
                                                                             txidpix,
                                                                             usuario.IdOperador, null, 0, 0, (int)pg, itensPorPagina);
                var model = new List<_PartialPesquisarModel>();
                model = mapper.Map<List<Cobranca.SelectPessoaDadosBasicosPesquisarReturnModel>, List<_PartialPesquisarModel>>(dados);
                return PartialView("_partialpesquisar", model.OrderByDescending(x => x.DataCadastro).ToPagedList(pg.Value, (itensPorPagina == 0 ? Constant.Paginacao.NumeroDefaultRegistroPorPagina : itensPorPagina)));
            }
            catch (Exception ex)
            {
                TempData["Mensagem"] = new MensagemModel(MensagemModel.TipoMensagem.Erro, Resource.MensagemErroPadrao, ex); ;
                return RedirectToAction("index", "mensagem");
            }
        }

        public virtual async Task<ActionResult> Detalhe(int id)
        {
            try
            {
                var usuarioAdm = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();

                if (TempData["Mensagem"] != null)
                    ViewBag.Mensagem = TempData["Mensagem"];
                var model = new DetalheCobrancaModel();
                model.Cobranca = mapper.Map<Cobranca.SelectCobrancaPorIdReturnModel, DetalheCobrancaModel.DadosCobranca>(new Cobranca(id).SelectCobrancaPorId(id, usuarioAdm.IdOperador));
                model.Cliente = mapper.Map<PigPag.DataModel.Cliente.SelectClienteReturnModel, DetalheCobrancaModel.DadosCliente>(await new Cliente(model.Cobranca.IdCliente).SelectClienteId());
                model.PessoaCobranca = mapper.Map<PessoaCobranca.SelectPessoaCobrancaPorIdReturnModel, DetalheCobrancaModel.DadosPessoaCliente>(new PessoaCobranca().SelectPessoaCobrancaId(model.Cobranca.IdPessoaCobranca));
                model.TransacoesBitcoin = mapper.Map<List<TransacaoBitcoin.SelectTransacaoBitcoinPorIdCobrancaReturnModel>, List<DetalheCobrancaModel.DadosTransacaoBTC>>(new TransacaoBitcoin().SelectTransacaoBitcoinPorIdCobranca(model.Cobranca.Id));
                model.Boletos = mapper.Map<List<Boleto.SelectBoletoPorIdCobrancaReturnModel>, List<DetalheCobrancaModel.DadosBoleto>>(new Boleto().SelectBoletoPorIdCobranca(model.Cobranca.Id));
                model.TransacoesCartaoCobranca = db.TransacaoCartaoCobranca.Where(x => x.IdCobranca == id).ToList();
                model.CapturasTransacaoCartaoCobranca = db.CapturaTransacaoCartaoCobranca.Where(x => x.IdCobranca == id).ToList();
                model.CartaoCobranca = db.CartaoCobranca.Where(x => x.IdCobranca == id).ToList();
                ViewBag.IdCartaoCobranca = new SelectList(from c in model.CartaoCobranca select new { Id = c.Id, Text = string.Format("{0}******{1}", c.CardBin, c.Last4) }, "Id", "Text");
                model.CancelamentosTransacaoCartaoCobranca = db.CancelamentoTransacaoCartaoCobranca.Where(x => x.IdCobranca == id).ToList();

                model.Parcelas = new List<DetalheCobrancaModel.DadosParcela>();
                foreach (var p in db.EntradaCliente.Where(x => x.IdCobranca == id).ToList())
                    model.Parcelas.Add(new DetalheCobrancaModel.DadosParcela()
                    {
                        DataBloqueio = p.DataBloqueio,
                        DataTransacao = p.DataTransacao,
                        DataDesbloqueio = p.DataDesbloqueio,
                        Parcela = p.Parcela,
                        ParcelaTotal = p.ParcelaTotal,
                        ValorAntecipado = p.ValorAntecipado,
                        ValorLiquido = p.ValorLiquido,
                        Descricao = p.Descricao,
                        DataCancelamento = p.DataCancelamento
                    });
                model.Chargeback = db.Chargeback.Where(x => x.IdCobranca == id).ToList();
                model.Itens = db.ItemCobranca.Where(x => x.IdCobranca == id).ToList();

                if (model.TransacoesCartaoCobranca.Count > 0)
                {
                    model.ComprovantesVenda = new List<DBDomain.ComprovanteVendaArquivoRetornoEDI>();
                    foreach (var t in model.TransacoesCartaoCobranca)
                        model.ComprovantesVenda.Add(db.ComprovanteVendaArquivoRetornoEDI.FirstOrDefault(x => x.IdTransacaoCartaoCobranca == t.Id));
                }
                return View(model);
            }
            catch (Exception ex)
            {
                TempData["Mensagem"] = new MensagemModel(MensagemModel.TipoMensagem.Erro, Resource.MensagemErroPadrao, ex); ;
                return RedirectToAction("index", "mensagem");
            }
        }

        public List<DetalheCobrancaModel.DadosLogCobranca> ObterLogsCobrancas(int id, string codigoCobranca)
        {
            try
            {
                var logs = MultiPay.DomainService.Mongo.Services.LogCobrancaService.Singleton
                .ObterLogsPorCobranca(id, codigoCobranca)
                .OrderBy(x => x.DataCriacaoUtc)
                .Select(item =>
                {
                    DetalheCobrancaModel.DadosLogCobranca parse = item;
                    return parse;
                })
                .ToList().OrderBy(x => x.Data).ToList();
                return logs;
            }
            catch
            {
                return new List<DetalheCobrancaModel.DadosLogCobranca>();
            }
        }

        public virtual async Task<ActionResult> ConfirmarPagamento(int id, byte idFormaPagamento, decimal valorPago, string txId)
        {
            try
            {
                var usuarioAdm = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                var logCobranca = new PigPag.Domain.Manager.LogCobranca();
                var cobranca = new Cobranca(id).SelectCobrancaPorId(id, usuarioAdm.IdOperador);
                if (cobranca == null)
                {
                    MensagemModel msg = new MensagemModel();
                    msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                    msg.Mensagem = "Cobrança não encontrada";
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }

                if (idFormaPagamento < 1)
                {
                    MensagemModel msg = new MensagemModel();
                    msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                    msg.Mensagem = "Selecione a forma de pagamento";
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }
                if (valorPago <= 0.00M)
                {
                    MensagemModel msg = new MensagemModel();
                    msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                    msg.Mensagem = "Valor pago não informado";
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }
                if ((idFormaPagamento == Constant.FormaPagamento.PIX || idFormaPagamento == Constant.FormaPagamento.Bitcoin) && string.IsNullOrEmpty(txId))
                {
                    MensagemModel msg = new MensagemModel();
                    msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                    msg.Mensagem = "Informe o TXId da transação";
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }
                var cliente = new Domain.Manager.Cliente(cobranca.IdCliente).SelectClienteDadosBasicoPorId(cobranca.IdCliente, usuarioAdm.IdOperador);

                cobranca = new Cobranca(id).SelectCobrancaPorId(id, usuarioAdm.IdOperador);
                valorPago = cobranca.ValorBruto;

                if (idFormaPagamento == Constant.FormaPagamento.Bitcoin)
                    await new Cobranca().ConfirmarPagamentoBTC(cobranca.Id, cobranca.IdCliente, cobranca.NumeroFatura, valorPago, txId);
                else
                {
                    if (idFormaPagamento == Constant.FormaPagamento.PIX)
                        await new Cobranca().ConfirmarPagamentoBRL(Constants.Constant.OperacaoServico.RecebimentoPIX, Constants.Constant.FormaPagamento.PIX, cobranca.Id, cobranca.IdCliente, cobranca.NumeroFatura, 1, valorPago, txId);
                    else
                        await new Cobranca().ConfirmarPagamentoBRL(cobranca.Id, cobranca.IdCliente, idFormaPagamento, cobranca.NumeroFatura, valorPago);
                }

                logCobranca.CadastrarLog(cobranca.Id, "Confirmação manual de cobrança feita por: " + new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado().Login);
                logCobranca.CadastrarLog(cobranca.Id, "Valor informado como pago: R$ " + valorPago.ToString(Constant.Mascaras.MascaraMoedaBRL));

                Dictionary<string, string> header = new Dictionary<string, string>();
                if (!string.IsNullOrWhiteSpace(cobranca.RequestIdWebhook))
                    header.Add("X-Request-Id", cobranca.RequestIdWebhook);

                if (!string.IsNullOrEmpty(cobranca.UrlConfirmacao))
                {
                    var idCobranca = cobranca.Id;
                    var urlWebhook = cobranca.UrlConfirmacao;
                    var confirmacaoCliente = await new UrlPost(urlWebhook).SendPaymentConfirmationUrlAsync(cliente.Guid,
                                                                  cobranca.NumeroFatura,
                                                                  cobranca.Guid,
                                                                  cobranca.DataPagamento.Value,
                                                                  cobranca.ValorLiquido,
                                                                  cobranca.ValorPago.Value,
                                                                  cobranca.MoedaCobranca,
                                                                  cobranca.MoedaPagamento,
                                                                  cobranca.SiglaFormaPagamento,
                                                                  cobranca.QuantidadeParcelasEscolhida,
                                                                  null,
                                                                  null, header);
                    if (confirmacaoCliente != null)
                    {
                        await new Domain.Manager.EnvioWebhookCobranca().InsertEnvioWebhookCobranca(idCobranca,
                                                    "CONFIRMAÇÃO DE PAGAMENTO",
                                                     urlWebhook,
                                                     confirmacaoCliente.RequestJson,
                                                     confirmacaoCliente.HttpResponseCode,
                                                     confirmacaoCliente.ResponseJson);
                        new PigPag.DBDomain.LogCobranca().CadastrarLog(idCobranca, "HttpResponseCode: " + confirmacaoCliente.HttpResponseCode);
                        new PigPag.DBDomain.LogCobranca().CadastrarLog(idCobranca, "HttpResponseBody: " + confirmacaoCliente.ResponseJson);
                    }
                }
                TempData["Mensagem"] = string.Format(Resource.MensagemConfirmacaoPagamento, cobranca.Id);
                return RedirectToAction("Index", "CobrancaPesquisar");
            }
            catch (Exception ex)
            {
                MensagemModel msg = new MensagemModel();
                msg.Tipo = MensagemModel.TipoMensagem.Erro;
                msg.Mensagem = Resource.MensagemErroPadrao + "<br /> ERRO: " + ex.Message;
                TempData["Mensagem"] = msg;
                return RedirectToRoute("mensagem");
            }
        }

        [HttpPost]
        public void ReenviarCobrancaFilaAsync(string txid, decimal valorPago)
        {
            var bus = serviceProvider.GetRequiredService<IBusReenviarCobranca>();
            bus.Publicar(new MultiPay.Plugin.RabbitMqBase.Events.ReenviarCobrancaEvent(txid, valorPago));
        }

        public virtual Task<string> ReenviarConfirmacaoPagamento(int[] ids)
        {
            var usuarioLogado = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();
            var bus = serviceProvider.GetRequiredService<IBusReenviarCobranca>();

            var semaphore = new SemaphoreSlim(20);

            _ = Task.Run(async () =>
            {
                var tasks = ids.Select(async id =>
                {
                    await semaphore.WaitAsync();

                    try
                    {
                        var cobranca = new Cobranca(id).SelectCobrancaPorId(id, usuarioLogado.IdOperador);

                        new DBDomain.LogCobranca().CadastrarLog(cobranca.Id, "[Enviado para mensageria] Reenvio de confirmação solicitada por: " + usuarioLogado.Login);

                        var qrcode = await db.QRCodePIX.Where(item => item.IdCobranca == cobranca.Id).AsNoTracking().FirstOrDefaultAsync();

                        bus.Publicar(new ReenviarCobrancaEvent(qrcode.TXId, cobranca.ValorPago.Value));
                    }
                    catch
                    {
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(tasks);
            });

            return Task.FromResult("Reenvio iniciado com sucesso. Os webhooks estão sendo enviados em segundo plano.");
        }

        /// <summary>
        /// 🆕 Método para reenvio individual de webhook (processamento sequencial)
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> ReenviarWebhookIndividual(int id)
        {
            try
            {
                var usuarioLogado = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();
                var cobranca = new Cobranca(id).SelectCobrancaPorId(id, usuarioLogado.IdOperador);

                if (cobranca == null)
                {
                    return Json(new { success = false, error = "Cobrança não encontrada" });
                }

                // Log da operação
                new DBDomain.LogCobranca().CadastrarLog(cobranca.Id,
                    $"[Processamento Individual] Reenvio de confirmação solicitada por: {usuarioLogado.Login}");

                // Buscar dados do PIX
                var qrcode = await db.QRCodePIX.Where(item => item.IdCobranca == cobranca.Id).AsNoTracking().FirstOrDefaultAsync();

                if (qrcode == null || !cobranca.ValorPago.HasValue)
                {
                    return Json(new { success = false, error = "Dados do PIX não encontrados ou cobrança não paga" });
                }

                // Publicar evento para reenvio
                var bus = serviceProvider.GetRequiredService<IBusReenviarCobranca>();
                bus.Publicar(new ReenviarCobrancaEvent(qrcode.TXId, cobranca.ValorPago.Value));

                return Json(new {
                    success = true,
                    message = $"Webhook da cobrança {cobranca.Codigo} enviado com sucesso",
                    codigoCobranca = cobranca.Codigo
                });
            }
            catch (Exception ex)
            {
                return Json(new {
                    success = false,
                    error = $"Erro ao processar webhook: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 🆕 Método para processamento em background (volumes grandes)
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> ReenviarConfirmacaoPagamentoBackground(int[] ids, string processId)
        {
            try
            {
                var usuarioLogado = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();

                // Log do início do processamento
                foreach (var id in ids)
                {
                    try
                    {
                        var cobranca = new Cobranca(id).SelectCobrancaPorId(id, usuarioLogado.IdOperador);
                        if (cobranca != null)
                        {
                            new DBDomain.LogCobranca().CadastrarLog(cobranca.Id,
                                $"[Processamento Background] ID: {processId} - Reenvio iniciado por: {usuarioLogado.Login}");
                        }
                    }
                    catch
                    {
                        // Continua mesmo se houver erro no log
                    }
                }

                // Usar o método existente para processamento em background
                var result = await ReenviarConfirmacaoPagamento(ids);

                return Json(new {
                    success = true,
                    processId = processId,
                    message = result,
                    totalItems = ids.Length,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return Json(new {
                    success = false,
                    processId = processId,
                    error = $"Erro ao iniciar processamento em background: {ex.Message}"
                });
            }
        }

        public ActionResult _PartialLogCobranca(string id)
        {
            DetalheCobrancaModel.DadosLogCobranca logCobranca = MultiPay.DomainService.Mongo.Services.LogCobrancaService.Singleton.GetById(id);

            return PartialView("_PartialLogCobranca", new PigPag.Manager.Models.Cobranca.LogCobrancaModel() { Data = logCobranca.Data, Descricao = logCobranca.Descricao, IP = logCobranca.IP, JSON = logCobranca.JSON });
        }

        [HttpPost]
        public virtual async Task<ActionResult> CancelarCobrancaPaga(int idCobranca, string motivo)
        {
            try
            {
                var usuarioAdm = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                var cobranca = new Cobranca(idCobranca).SelectCobrancaPorId(idCobranca, usuarioAdm.IdOperador);
                if (cobranca == null)
                {
                    MensagemModel msg = new MensagemModel();
                    msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                    msg.Mensagem = "Cobrança não encontrada";
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }

                var cliente = await db.Cliente.SingleAsync(x => x.Id == cobranca.IdCliente);
                var transacaoCartaoCobranca = await db.TransacaoCartaoCobranca.FirstOrDefaultAsync(x => x.IdCobranca == cobranca.Id && x.Autorizada && x.Capturada);

                // Cancelamento da Adquirente
                if (transacaoCartaoCobranca != null)
                {
                    var terminal = await db.Terminal.FirstOrDefaultAsync(x => x.Id == transacaoCartaoCobranca.IdTerminal);
                    if (cobranca.DataPagamento.Value.Date < DateTime.Now.Date)
                    {
                        var cancelTransaction = await new PigPagTransaction(Request.Url.OriginalString.Contains("localhost") ?
                                                       PigPagEnvironment.Production :
                                                       PigPagEnvironment.Sandbox).CancellationTransactionHub(terminal.ClientID,
                                                                                                             terminal.ClientSecret,
                                                                                                             terminal.MID,
                                                                                                             transacaoCartaoCobranca.DataCaptura.Value,
                                                                                                             transacaoCartaoCobranca.NSU,
                                                                                                             transacaoCartaoCobranca.AuthorizationCode,
                                                                                                             transacaoCartaoCobranca.ValorTransacao.Value,
                                                                                                             transacaoCartaoCobranca.ValorTransacao.Value,
                                                                                                             motivo,
                                                                                                             cobranca.Id);
                        // Transação autorizada e capturada
                        if (cancelTransaction.Code == 0 && !cancelTransaction.Error)
                        {
                            new Cobranca().CancelPayment(cobranca.Id, cobranca.IdCliente, cobranca.NumeroFatura, cobranca.IdFormaPagamento.Value, cobranca.QuantidadeParcelasEscolhida,
                                                         cobranca.ValorPago.Value, cobranca.ValorLiquido, cobranca.ValorPago.Value, motivo, null,
                                                         new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado().IdUsuarioAdministrativo,
                                                         cancelTransaction.data.FirstOrDefault().id, cancelTransaction.data.FirstOrDefault().id,
                                                         cancelTransaction.Code.ToString(), transacaoCartaoCobranca.AuthorizationCode, cancelTransaction.Message,
                                                         null, "canceled", null, "ADIQ");
                        }
                        else
                        {
                            MensagemModel msg = new MensagemModel();
                            msg.Tipo = MensagemModel.TipoMensagem.Erro;
                            msg.Mensagem = "Cobrança não foi cancelada devido erro no sistema";
                            TempData["Mensagem"] = msg;
                            return RedirectToRoute("mensagem");
                        }
                    }
                    else
                    {
                        var cancelTransaction = new PigPagTransaction(!Request.Url.OriginalString.Contains("localhost") ?
                                                       PigPagEnvironment.Production :
                                                       PigPagEnvironment.Sandbox).CancellationTransaction(terminal.ClientID, terminal.ClientSecret, transacaoCartaoCobranca.TID, transacaoCartaoCobranca.ValorTransacao.Value, cobranca.Id);
                        // Transação autorizada e capturada
                        if (cancelTransaction.Code == "00" && !cancelTransaction.Error)
                        {
                            new Cobranca().CancelPayment(cobranca.Id, cobranca.IdCliente, cobranca.NumeroFatura, cobranca.IdFormaPagamento.Value, cobranca.QuantidadeParcelasEscolhida, cobranca.ValorPago.Value, cobranca.ValorLiquido, cobranca.ValorPago.Value, motivo, null, new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado().IdUsuarioAdministrativo, cancelTransaction.CancelAuthorization.CancelAuthorizationCode, cancelTransaction.CancelAuthorization.CancelAuthorizationCode, cancelTransaction.Code, cancelTransaction.CancelAuthorization.PaymentId, cancelTransaction.Message, null, "canceled", null, "ADIQ");
                        }
                        else
                        {
                            MensagemModel msg = new MensagemModel();
                            msg.Tipo = MensagemModel.TipoMensagem.Erro;
                            msg.Mensagem = "Cobrança não foi cancelada devido erro no sistema";
                            TempData["Mensagem"] = msg;
                            return RedirectToRoute("mensagem");
                        }
                    }
                }
                else
                {
                    new Cobranca().CancelPayment(cobranca.Id, cobranca.IdCliente, cobranca.NumeroFatura, cobranca.IdFormaPagamento.Value, cobranca.QuantidadeParcelasEscolhida, cobranca.ValorPago.Value, cobranca.ValorLiquido, cobranca.ValorPago.Value, motivo, null, new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado().IdUsuarioAdministrativo, null, null, null, null, null, null, "canceled", null, "ADIQ");
                }

                TempData["Mensagem"] = "Cobrança cancelada com sucesso";
                return RedirectToAction("Index", "CobrancaPesquisar");
            }
            catch (Exception ex)
            {
                MensagemModel msg = new MensagemModel();
                msg.Tipo = MensagemModel.TipoMensagem.Erro;
                msg.Mensagem = Resource.MensagemErroPadrao + "<br /> ERRO: " + ex.Message;
                TempData["Mensagem"] = msg;
                return RedirectToRoute("mensagem");
            }
        }

        public ActionResult _PartialMostrarDadosCartao(string pwd, int id, int idCobranca)
        {
            var usuarioLogado = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
            if (!new UsuarioAdministrativo().FazParteFinanceiro())
                return PartialView("_PartialAcessoNegado");
            if (!new UsuarioAdministrativo().ValidarSenhaUsuario(usuarioLogado.IdUsuarioAdministrativo, pwd))
                return PartialView("_PartialAcessoNegado");
            else
            {
                var cartao = new PigPagCartaoCobranca(id, idCobranca).Select(true);
                return PartialView("_PartialDadosCartao", new PigPag.DBDomain.CartaoCobranca()
                {
                    CardBin = cartao.CardBin,
                    CPFCNPJTitular = cartao.CPFCNPJTitular,
                    CVV = cartao.CVV,
                    DataCadastro = cartao.DataCadastro,
                    DataExclusao = cartao.DataExclusao,
                    Id = cartao.Id,
                    IdCobranca = cartao.IdCobranca,
                    Last4 = cartao.Last4,
                    NomeTitular = cartao.NomeTitular,
                    NumeroCartao = cartao.NumeroCartao,
                    Validade = cartao.Validade
                });
            }
        }

        public ActionResult _PartialChavePrivadaEndereco(string pwd, int id, int idCobranca)
        {
            var usuarioLogado = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
            if (!new UsuarioAdministrativo().FazParteFinanceiro())
                return PartialView("_PartialAcessoNegado");
            if (!new UsuarioAdministrativo().ValidarSenhaUsuario(usuarioLogado.IdUsuarioAdministrativo, pwd))
                return PartialView("_PartialAcessoNegado");
            else
            {
                var endereco = db.EnderecoBitcoin.FirstOrDefault(x => x.Id == id);
                return PartialView("_PartialChavePrivada", (object)new PigPag.Security.Cryptography.Cryptography().Decrypt(endereco.PrivateKey));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public virtual async Task<string> AbrirContestacaoCobranca(AbrirContestacaoCobrancaModel model)
        {
            try
            {
                bool transacaoCartao = true;

                if (model.IdCartaoCobranca < 1 && !string.IsNullOrWhiteSpace(model.Endtoend))
                {
                    transacaoCartao = false;
                }
                var usuario = new UsuarioAdministrativoModel().SelectUsuarioAdministrativoLogado();
                if (model.IdCobranca < 1)
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, "Cobrancaça não identificada!"));
                if (string.IsNullOrEmpty(model.Motivo))
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, "Informe o motivo da contestação!"));
                var cobranca = new Cobranca().SelectCobrancaPorId(model.IdCobranca, usuario.IdOperador);
                if (cobranca == null)
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, "Cobrancaça não identificada!"));

                if (model.ValorContestado < 0.00M)
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, "Valor contestado inválido!"));

                DBDomain.TransacaoCartaoCobranca transacaoCartaoCobranca = null;
                DBDomain.CartaoCobranca cartaoCobranca = null;

                decimal valorTransacao = 0;
                DateTime dataTransacao;
                var nsu = string.Empty;

                if (transacaoCartao)
                {
                    if (model.IdCartaoCobranca < 1)
                        return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, "Cartão cobrança não localizado!"));

                    cartaoCobranca = db.CartaoCobranca.SingleOrDefault(x => x.IdCobranca == model.IdCobranca && x.Id == model.IdCartaoCobranca);
                    if (cartaoCobranca == null)
                        return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, "Cartão cobrança não localizado!"));
                    else if (cartaoCobranca.IdCobranca != model.IdCobranca)
                        return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, "Cartão cobrança não pertence a esta cobrança!"));
                    transacaoCartaoCobranca = db.TransacaoCartaoCobranca
                        .Include("BandeiraCartao")
                        .SingleOrDefault(x => x.IdCobranca == model.IdCobranca && x.IdCartaoCobranca == cartaoCobranca.Id && x.Autorizada);
                    if (transacaoCartaoCobranca == null)
                        return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, "Transação não localizada!"));

                    valorTransacao = transacaoCartaoCobranca.ValorTransacao.Value;
                    dataTransacao = transacaoCartaoCobranca.DataTransacao;
                    nsu = transacaoCartaoCobranca.NSU;
                }
                else
                {
                    var qrCodePix = await db.QRCodePIX
                    .Where(item => item.DataPagamento.HasValue && item.ValorPago.HasValue)
                    .Where(item => item.IdCobranca == model.IdCobranca)
                    .Select(item => new
                    {
                        item.EndToEndPagamento,
                        item.ValorPago,
                        item.DataPagamento
                    })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                    valorTransacao = qrCodePix.ValorPago.Value;
                    dataTransacao = qrCodePix.DataPagamento.Value;
                    nsu = qrCodePix.EndToEndPagamento ?? model.Endtoend;
                }

                var cliente = await new Cliente().SelectClienteId(cobranca.IdCliente);
                var contato = await new ContatoPessoa().SelectContatoPessoaPrincipalPorIdPessoa(cliente.IdPessoa);

                DBDomain.AjusteACreditoADebitoArquivoRetornoEDI ajuste = null;
                if (model.IdAjusteADebito.HasValue)
                {
                    ajuste = db.AjusteACreditoADebitoArquivoRetornoEDI.Single(x => x.Id == model.IdAjusteADebito.Value);
                    var ajusteMesmaTranscao = db.AjusteACreditoADebitoArquivoRetornoEDI.Where(x => x.IdTransacaoCartaoCobrancaOriginal == ajuste.IdTransacaoCartaoCobrancaOriginal).ToList();
                    foreach (var a in ajusteMesmaTranscao)
                    {
                        a.DataConclusao = DateTime.Now;
                        a.IdUsuarioAdministrativoConcluiu = usuario.IdUsuarioAdministrativo;
                        db.Entry(a).State = EntityState.Modified;
                    }
                }

                var chargeback = new DBDomain.Chargeback()
                {
                    IdBandeira = transacaoCartaoCobranca?.IdBandeira ?? 0,
                    IdCartaoCobranca = cartaoCobranca?.Id,
                    IdTransacaoCartaoCobranca = transacaoCartaoCobranca?.Id,
                    IdCliente = cobranca.IdCliente,
                    IdCobranca = cobranca.Id,
                    Codigo = Common.GeradorId.GetBase36(5),
                    DataContestacao = DateTime.Now,
                    Motivo = model.Motivo.Trim().ToUpper(),
                    NSU = nsu,
                    ValorTransacao = valorTransacao,
                    ValorContestado = model.ValorContestado,
                    DataTransacao = dataTransacao,
                    PrazoResposta = ajuste == null ? DateTime.Now.AddDays(5) : ajuste.DataLancamentoAjuste > DateTime.Now ? ajuste.DataLancamentoAjuste : DateTime.Now,
                    Revertido = false,
                    DataConclusao = ajuste == null ? (DateTime?)null : ajuste.DataLancamentoAjuste <= DateTime.Now ? DateTime.Now : (DateTime?)null,
                    IdUsuarioAdministrativoEnviouEmissor = ajuste == null ? (int?)null : ajuste.DataLancamentoAjuste <= DateTime.Now ? usuario.IdUsuarioAdministrativo : (int?)null,
                };
                if (ajuste != null && ajuste.DataLancamentoAjuste <= DateTime.Now)
                    chargeback.ObservacaoChargeback.Add(new DBDomain.ObservacaoChargeback()
                    {
                        IdUsuarioAdministrativo = usuario.IdUsuarioAdministrativo,
                        Observacao = "CHARGEBACK COMPULSÓRIO DO EMISSOR",
                        DataCadastro = DateTime.Now,
                    });

                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        db.Chargeback.Add(chargeback);

                        var entradasCobranca = db.EntradaCliente.Where(x => x.IdCliente == cobranca.IdCliente && x.IdCobranca == cobranca.Id && !x.DataCancelamento.HasValue && !x.DataBloqueio.HasValue).ToList();
                        if (transacaoCartao)
                        {
                            var saldoLiquidoAgenda = entradasCobranca.Where(x => x.DataDesbloqueio.Date > DateTime.Now.Date).ToList();
                            // Bloqueando a agenda ainda disponível
                            if (saldoLiquidoAgenda.Count > 0)
                            {
                                // Bloqueando os saldos ainda disponível para receber
                                foreach (var e in saldoLiquidoAgenda.Where(x => x.ValorLiquido - x.ValorAntecipado > 0.00M).ToList())
                                {
                                    e.DataBloqueio = DateTime.Now;
                                    db.Entry(e).State = EntityState.Modified;
                                }
                                // Retirando o saldo já antecipado da agenda ainda diponível
                                var saldoJaAntecipadoAgenda = saldoLiquidoAgenda.Where(x => x.Antecipacao).ToList().Sum(x => x.ValorLiquido);
                                if (saldoJaAntecipadoAgenda > 0.00M)
                                    await new SaidaCliente().InsertSaidaClienteExtratoContabil(new SaidaCliente.InsertSaidaClienteExtratoContabilRequestModel()
                                    {
                                        DataDesloqueio = DateTime.Now,
                                        Descricao = "CHARGEBACK COB " + cobranca.Codigo + " - SALDO ANTECIPADO",
                                        IdCliente = cobranca.IdCliente,
                                        IdMoeda = Constant.Moeda.IdMoedaBRL,
                                        Valor = saldoJaAntecipadoAgenda,
                                        ValorTarifa = 0.00M,
                                        ValorTaxa = 0.00M
                                    });
                            }
                        }
                        var saldoLiquidoRecebido = entradasCobranca.Where(x => x.DataDesbloqueio.Date <= DateTime.Now.Date).ToList();

                        // Lançando débito da agenda já recebida ou antecipada
                        if (saldoLiquidoRecebido.Count > 0)
                        {
                            var saldoJaRecebido = saldoLiquidoRecebido.Where(x => !x.Antecipacao).Sum(x => x.ValorLiquido - x.ValorAntecipado);
                            if (saldoJaRecebido > 0.00M)
                                await new SaidaCliente().InsertSaidaClienteExtratoContabil(new SaidaCliente.InsertSaidaClienteExtratoContabilRequestModel()
                                {
                                    DataDesloqueio = DateTime.Now,
                                    Descricao = (transacaoCartao ? "CHARGEBACK COB " : "CONTESTAÇÃO COB ") + cobranca.Codigo + " - SALDO JA RECEBIDO",
                                    IdCliente = cobranca.IdCliente,
                                    IdMoeda = Constant.Moeda.IdMoedaBRL,
                                    Valor = saldoJaRecebido,
                                    ValorTarifa = 0.00M,
                                    ValorTaxa = 0.00M
                                });
                            var saldoJaRecebidoAntecipado = saldoLiquidoRecebido.Where(x => x.Antecipacao).Sum(x => x.ValorLiquido);
                            if (saldoJaRecebidoAntecipado > 0.00M)
                                await new SaidaCliente().InsertSaidaClienteExtratoContabil(new SaidaCliente.InsertSaidaClienteExtratoContabilRequestModel()
                                {
                                    DataDesloqueio = DateTime.Now,
                                    Descricao = (transacaoCartao ? "CHARGEBACK COB " : "CONTESTAÇÃO COB ") + cobranca.Codigo + " - SALDO ANTECIPADO",
                                    IdCliente = cobranca.IdCliente,
                                    IdMoeda = Constant.Moeda.IdMoedaBRL,
                                    Valor = saldoJaRecebidoAntecipado,
                                    ValorTarifa = 0.00M,
                                    ValorTaxa = 0.00M
                                });
                        }
                        db.SaveChanges();
                        transaction.Commit();
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }

                if (!Request.Url.OriginalString.Contains("localhost") && contato != null)
                {
                    try
                    {
                        new Thread(() =>
                        {
                            Thread.CurrentThread.IsBackground = true;
                            //Enviar e-mail
                            var body =
                                (transacaoCartao ? Resource.EmailAberturaContestacaoCobranca : Resource.EmailAberturaContestacaoCobrancaPix)
                                .Replace("[NOME_PESSOA]", contato.Nome)
                                .Replace("[CODIGO_COBRANCA]", cobranca.Codigo)
                                .Replace("[DATA_PRAZO]", DateTime.Now.AddDays(5).ToShortDateString())
                                .Replace("[NUMERO_CARTAO]", string.Format("{0}******{1}", cartaoCobranca?.CardBin, cartaoCobranca?.Last4))
                                .Replace("[NSU]", nsu)
                                .Replace("[BANDEIRA]", transacaoCartaoCobranca?.BandeiraCartao?.Nome?.ToUpper())
                                .Replace("[TRANSACAO_ORIGINAL]", valorTransacao.ToString(Constant.Mascaras.MascaraMoedaBRL))
                                .Replace("[VALOR_CONTESTADO]", model.ValorContestado.ToString(Constant.Mascaras.MascaraMoedaBRL))
                                .Replace("[DATA_AUTORIZACAO]", dataTransacao.ToShortDateString())
                                .Replace("[MOTIVO]", model.Motivo.Trim().ToUpperInvariant());

                            new PigPag.Email.Email().Enviar("PigPag - Solicitação de saque para própria conta", new Email.Email.Destinatario() { email = new System.Net.Mail.MailAddress(contato.Email.Trim(), contato.Nome), tipo = Email.Email.TipoDestinatario.Normal }, body);
                        }).Start();
                    }
                    catch { }
                }
                return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.success, false, true, "Contestação feita com sucesso!"));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        [HttpGet]
        public ActionResult AprovarCobrancaListaTxId()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public virtual async Task<ActionResult> AprovarCobrancaListaTxId(HttpPostedFileBase uploadArquivoRetorno)
        {
            try
            {
                var usuarioAdm = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                MensagemModel msg = new MensagemModel();
                //string path = Server.MapPath(@"~\Arquivos\Cobranca\TxIdPagos");
                //if (!Directory.Exists(path))
                //    Directory.CreateDirectory(path);
                //string numeroArquivo = (Directory.GetFiles(path, "*", SearchOption.AllDirectories).Length + 1).ToString().PadLeft(4, '0');
                //string arquivo = path + @"\" + DateTime.Now.ToString("yyyyMMdd") + @"_" + numeroArquivo + ".txt";
                //uploadArquivoRetorno.SaveAs(arquivo);

                List<string> linhasArquivo = new List<string>();
                string linha = "";
                if (uploadArquivoRetorno.ContentLength > 0)
                {
                    using (StreamReader texto = new StreamReader(uploadArquivoRetorno.InputStream))
                    {
                        while ((linha = texto.ReadLine()) != null)
                        {
                            linhasArquivo.Add(linha);
                        }
                    }

                    if (linhasArquivo.Count > 100000)
                    {
                        msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                        msg.Mensagem = "Seu arquivo não pode conter mais de 100000 linhas";
                        TempData["Mensagem"] = msg;
                        return RedirectToRoute("mensagem");
                    }

                    for (var i = 0; i < linhasArquivo.Count; i++)
                    {
                        var transacaoPix = await new QRCodePIX().SelectPorTxId(linhasArquivo[i]);
                        if (transacaoPix == null) continue;
                        var cobranca = new Cobranca().SelectCobrancaPorId(transacaoPix.IdCobranca, usuarioAdm.IdOperador);
                        if (cobranca == null) continue;

                        if (cobranca != null && !cobranca.DataPagamento.HasValue)
                        {
                            new Thread(async () =>
                            {
                                Thread.CurrentThread.IsBackground = true;

                                var loginUsuario = usuarioAdm.Login;
                                var cobranca1 = new Cobranca().SelectCobrancaPorId(transacaoPix.IdCobranca, usuarioAdm.IdOperador);

                                // Confirmação de pagamento via PIX
                                new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, "Confirmando pagamento via TXID");
                                new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, "Usuário da confirmação: " + loginUsuario);
                                await new Cobranca().ConfirmarPagamentoBRL(PigPag.Constants.Constant.OperacaoServico.RecebimentoPIX, PigPag.Constants.Constant.FormaPagamento.PIX, cobranca.Id, cobranca.IdCliente, cobranca.NumeroFatura, 1, cobranca.ValorLiquido, linhasArquivo[i]);
                                if (!string.IsNullOrEmpty(cobranca1.UrlConfirmacao))
                                {
                                    new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, "Enviando confirmação de pagamento");
                                    cobranca = new Domain.Manager.Cobranca(cobranca1.Id).SelectCobrancaPorId(cobranca1.Id, null);
                                    var cliente = new Domain.Manager.Cliente().SelectClienteDadosBasicoPorId(cobranca1.IdCliente, usuarioAdm.IdOperador);
                                    var confirmacaoCliente = await new UrlPost(cobranca1.UrlConfirmacao).SendPaymentConfirmationUrlAsync(cliente.Guid,
                                                                              cobranca1.NumeroFatura,
                                                                              cobranca1.Guid,
                                                                              cobranca1.DataPagamento.Value,
                                                                              cobranca1.ValorLiquido,
                                                                              cobranca1.ValorPago.Value,
                                                                              cobranca1.MoedaCobranca,
                                                                              cobranca1.MoedaPagamento,
                                                                              cobranca1.NomeFormaPagamento,
                                                                              cobranca1.QuantidadeParcelasEscolhida,
                                                                              null,
                                                                              null);
                                    if (confirmacaoCliente != null)
                                    {
                                        await new Domain.Manager.EnvioWebhookCobranca().InsertEnvioWebhookCobranca(cobranca1.Id,
                                                                        "CONFIRMAÇÃO DE PAGAMENTO",
                                                                         cobranca1.UrlConfirmacao,
                                                                         confirmacaoCliente.RequestJson,
                                                                         confirmacaoCliente.HttpResponseCode,
                                                                         confirmacaoCliente.ResponseJson);
                                    }
                                    new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, string.Format("Resposta: {0}", confirmacaoCliente.ResponseJson));
                                    new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, "Confirmação de pagamento reenviada com sucesso");
                                }
                            }).Start();
                        }
                        //if ((Convert.ToDecimal(i) % 20.00M) == 0)
                        //{
                        //    Console.WriteLine("Aguardando 2s...");
                        //    Thread.Sleep(5000);
                        //}
                    }

                    msg.Tipo = MensagemModel.TipoMensagem.Sucesso;
                    msg.Mensagem = Resource.MensagemArquivoProcessadoSucesso;
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }
                else
                {
                    msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                    msg.Mensagem = Resource.MensagemArquivoRetornoNaoSelecionado;
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }
            }
            catch (Exception ex)
            {
                MensagemModel msg = new MensagemModel();
                msg.Tipo = MensagemModel.TipoMensagem.Erro;
                msg.Mensagem = Resource.MensagemErroPadrao + "<br /> ERRO: " + ex.Message;
                TempData["Mensagem"] = msg;
                return RedirectToRoute("mensagem");
            }
        }

        [HttpGet]
        public ActionResult AprovarCobrancaListaTxIdParaWebhook()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public virtual async Task<ActionResult> AprovarCobrancaListaTxIdParaWebhook(HttpPostedFileBase uploadArquivoRetorno)
        {
            try
            {
                var usuarioAdm = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                MensagemModel msg = new MensagemModel();
                //string path = Server.MapPath(@"~\Arquivos\Cobranca\TxIdPagos\Webhook");
                //if (!Directory.Exists(path))
                //    Directory.CreateDirectory(path);
                //string numeroArquivo = (Directory.GetFiles(path, "*", SearchOption.AllDirectories).Length + 1).ToString().PadLeft(4, '0');
                //string arquivo = path + @"\" + DateTime.Now.ToString("yyyyMMdd") + @"_" + numeroArquivo + ".txt";
                //uploadArquivoRetorno.SaveAs(arquivo);

                List<string> linhasArquivo = new List<string>();
                string linha = "";
                if (uploadArquivoRetorno.ContentLength > 0)
                {
                    using (StreamReader texto = new StreamReader(uploadArquivoRetorno.InputStream))
                    {
                        while ((linha = texto.ReadLine()) != null)
                        {
                            linhasArquivo.Add(linha);
                        }
                    }

                    if (linhasArquivo.Count > 100000)
                    {
                        msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                        msg.Mensagem = "Seu arquivo não pode conter mais de 100000 linhas";
                        TempData["Mensagem"] = msg;
                        return RedirectToRoute("mensagem");
                    }

                    foreach (var txid in linhasArquivo)
                    {
                        var transacaoPix = await new QRCodePIX().SelectPorTxId(txid);
                        if (transacaoPix == null) continue;
                        var cobranca = new Cobranca().SelectCobrancaPorId(transacaoPix.IdCobranca, usuarioAdm.IdOperador);
                        if (cobranca == null) continue;
                        if (cobranca != null && cobranca.DataPagamento.HasValue && !string.IsNullOrEmpty(cobranca.UrlConfirmacao))
                        {
                            new Thread(async () =>
                            {
                                var loginUsuario = usuarioAdm.Login;
                                var cobranca1 = new Domain.Manager.Cobranca(cobranca.Id).SelectCobrancaPorId(cobranca.Id, usuarioAdm.IdOperador);

                                new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, "Enviando confirmação pagamento via TXID");
                                new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, "Usuário que enviou a solicitação: " + loginUsuario);
                                new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, "Enviando confirmação de pagamento");
                                var cliente = new Domain.Manager.Cliente().SelectClienteDadosBasicoPorId(cobranca1.IdCliente, usuarioAdm.IdOperador);
                                var confirmacaoCliente = await new UrlPost(cobranca1.UrlConfirmacao).SendPaymentConfirmationUrlAsync(cliente.Guid,
                                                                      cobranca1.NumeroFatura,
                                                                      cobranca1.Guid,
                                                                      cobranca1.DataPagamento.Value,
                                                                      cobranca1.ValorLiquido,
                                                                      cobranca1.ValorPago.Value,
                                                                      cobranca1.MoedaCobranca,
                                                                      cobranca1.MoedaPagamento,
                                                                      cobranca1.NomeFormaPagamento,
                                                                      cobranca1.QuantidadeParcelasEscolhida,
                                                                      null,
                                                                      null);
                                if (confirmacaoCliente != null)
                                {
                                    await new Domain.Manager.EnvioWebhookCobranca().InsertEnvioWebhookCobranca(cobranca1.Id,
                                                                "CONFIRMAÇÃO DE PAGAMENTO",
                                                                 cobranca1.UrlConfirmacao,
                                                                 confirmacaoCliente.RequestJson,
                                                                 confirmacaoCliente.HttpResponseCode,
                                                                 confirmacaoCliente.ResponseJson);
                                }
                                new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, string.Format("Resposta: {0}", confirmacaoCliente.ResponseJson));
                                new PigPag.DBDomain.LogCobranca().CadastrarLog(cobranca1.Id, "Confirmação de pagamento reenviada com sucesso");
                                new Cobranca().ConfirmarEnvioConfirmacaoCliente(cobranca1.Id);
                            }).Start();
                        }

                        //if ((Convert.ToDecimal(i) % 40.00M) == 0)
                        //{
                        //    Console.WriteLine("Aguardando 2s...");
                        //    Thread.Sleep(2000);
                        //}
                    }

                    msg.Tipo = MensagemModel.TipoMensagem.Sucesso;
                    msg.Mensagem = Resource.MensagemArquivoProcessadoSucesso;
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }
                else
                {
                    msg.Tipo = MensagemModel.TipoMensagem.Alerta;
                    msg.Mensagem = Resource.MensagemArquivoRetornoNaoSelecionado;
                    TempData["Mensagem"] = msg;
                    return RedirectToRoute("mensagem");
                }
            }
            catch (Exception ex)
            {
                MensagemModel msg = new MensagemModel();
                msg.Tipo = MensagemModel.TipoMensagem.Erro;
                msg.Mensagem = Resource.MensagemErroPadrao + "<br /> ERRO: " + ex.Message;
                TempData["Mensagem"] = msg;
                return RedirectToRoute("mensagem");
            }
        }

        public virtual ActionResult PagadorCobranca(int id)
        {
            try
            {
                if (TempData["Mensagem"] != null)
                    ViewBag.Mensagem = TempData["Mensagem"];
                var model = mapper.Map<DataModel.Cobranca.SelectPagadorCobrancaDetalhePorId, PagadorCobrancaModel>(new Cobranca().SelectPagadorCobrancaDetalhePorId(id));

                return View(model);
            }
            catch (Exception ex)
            {
                TempData["Mensagem"] = new MensagemModel(MensagemModel.TipoMensagem.Erro, Resource.MensagemErroPadrao, ex); ;
                return RedirectToAction("index", "mensagem");
            }
        }

        public ActionResult _PartialLogCobrancaLista(int idCobranca, string codigoCobranca)
        {
            try
            {
                var logs = ObterLogsCobrancas(idCobranca, codigoCobranca);

                return View(logs);
            }
            catch (Exception ex)
            {
                TempData["Mensagem"] = new MensagemModel(MensagemModel.TipoMensagem.Erro, Resource.MensagemErroPadrao, ex); ;
                return RedirectToAction("index", "mensagem");
            }
        }

        public ActionResult ReenviarWebhookEmLote()
        {
            return View();
        }

        public virtual async Task<string> GetCobrancasParaReenviarWebhook(string di, string df)
        {
            try
            {
                if (string.IsNullOrEmpty(di))
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, $"Informe a data de início"));
                if (string.IsNullOrEmpty(df))
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, $"Informe a data de fim"));

                DateTime? dtI = null, dtF = null;
                if (!string.IsNullOrEmpty(di))
                    dtI = Convert.ToDateTime(di);
                if (!string.IsNullOrEmpty(df))
                    dtF = Convert.ToDateTime(df);

                var cobrancas = await db.Cobranca
                    .GroupJoin(db.QRCodePIX, c => c.Id, q => q.IdCobranca, (cob, qrcode) => new { cob, qrcode })
                    .SelectMany(item => item.qrcode.DefaultIfEmpty(),
                    (c, q) => new
                    {
                        c.cob.Codigo,
                        c.cob.Id,
                        c.cob.NumeroFatura,
                        c.cob.DataPagamento,
                        QRCodePIX = q != null,
                        TXId = q == null ? "" : q.TXId,
                        ValorPago = q == null ? 0 : (q.ValorPago ?? 0)
                    }).Where(x => x.DataPagamento.HasValue && x.DataPagamento >= dtI && x.DataPagamento <= dtF).ToListAsync();

                if (!cobrancas.Any())
                    return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.alert, true, false, $"Nenhuma cobrança encontrada na busca"));

                return JsonConvert.SerializeObject(cobrancas);
            }
            catch (Exception ex)
            {
                return JsonConvert.SerializeObject(new JSonReturnModel(JSonReturnModel.Type.error, true, false, $"ERRO: {ex.Message}"));
            }
        }
    }
}