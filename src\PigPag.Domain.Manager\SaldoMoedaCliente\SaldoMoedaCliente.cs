﻿using AutoMapper;
using PigPag.DataModel.SaldoMoedaCliente;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace PigPag.Domain.Manager
{
    public class SaldoMoedaCliente : DomainBase
    {
        public SaldoMoedaCliente()
        {
            var config = new MapperConfiguration(cfg =>
            {
            });
            mapper = config.CreateMapper();
        }

        public enum Moeda
        {
            Real = 1,
            Dolar = 2,
            Euro = 3,
            Bitcoin = 4
        };

        public async Task<decimal> SelectSaldoMoedaClienteSomatorioMoeda(Moeda moeda, CancellationToken cancellationToken)
        {
            try
            {
                var usuarioLogado = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                return await new DomainService.Manager.Service.SaldoMoedaCliente().SelectSaldoMoedaClienteSomatorioMoeda((byte)moeda, usuarioLogado.IdOperador, cancellationToken);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public virtual async Task<IList<SelectSaldoMoedaClienteReturnModel>> SelectSaldoMoedaCliente(Moeda moeda, int? idOperador = null)
        {
            try
            {
                return await SelectSaldoMoedaCliente((byte)moeda, 0.00M, idOperador);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public virtual async Task<IList<SelectSaldoMoedaClienteReturnModel>> SelectSaldoMoedaCliente(Moeda moeda, decimal saldoMinimo = 0.00M, int? idOperador = null)
        {
            try
            {
                return await SelectSaldoMoedaCliente((byte)moeda, saldoMinimo, idOperador);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public virtual async Task<IList<SelectSaldoMoedaClienteReturnModel>> SelectSaldoMoedaCliente(byte idMoeda, decimal saldoMinimo = 0.00M, int? idOperador = null)
        {
            try
            {
                return await new DomainService.Manager.Service.SaldoMoedaCliente().SelectSaldoMoedaCliente(idMoeda, saldoMinimo, idOperador);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}