using System;
using Multipay.Service.Criptography;

namespace PigPag.DomainService.Connection
{
    public class SQLServerConnection
    {
        private readonly bool isProd = false;
        private readonly bool isWriting = true;

        private string ReadingConnectionStringComplement
        {
            get
            {
                return !isProd || isWriting
                    ? String.Empty
                    : $"Database={DB_NAME};ApplicationIntent=ReadOnly;MultiSubnetFailover=True";
            }
        }

        public static ConnectionTypeEnum CurrentConnectionType =
            ConnectionTypeEnum.ConnectPSP_Hml_Manager;

        #region [ Parametros Iniciais Conexão ]

        protected string dbServer = string.Empty;
        protected string dbServerReading = string.Empty;
        protected string db = string.Empty;
        protected string dbUser = string.Empty;
        protected string dbPassword = string.Empty;
        private string applicationName = string.Empty;
        private int minPoolConnection = 100;
        private int maxPoolConnection = 1000;
        private static bool dbEncripted = true;

        #endregion [ Parametros Iniciais Conexão ]

        private static readonly CriptoService _criptoService = new CriptoService();

        public SQLServerConnection(string applicationName, bool isWriting = true)
            : this(applicationName, 100, 1200, isWriting) { }

        public SQLServerConnection(
            string applicationName,
            int minPool,
            int maxPool,
            bool isWriting = true
        )
        {
            this.applicationName = applicationName.Trim();
            this.minPoolConnection = minPool;
            this.maxPoolConnection = maxPool;
            this.isWriting = isWriting;

            SetConnectionInfo();
        }

        private static string _dbServer = null;
        private static string _dbServerReading = null;

        public string DB_SERVER
        {
            get
            {
                if (_dbServer != null)
                    return _dbServer;

                var environment = Environment.GetEnvironmentVariable("DS_SQLSERVER_MULTIPAY");
                _dbServer = dbServer;

                if (!string.IsNullOrWhiteSpace(environment))
                    _dbServer = environment;

                return _dbServer;
            }
        }

        public string DB_SERVER_READING
        {
            get
            {
                if (_dbServerReading != null)
                    return _dbServerReading;

                var environment = Environment.GetEnvironmentVariable(
                    "DS_SQLSERVER_READING_MULTIPAY"
                );
                _dbServerReading = dbServerReading;

                if (!string.IsNullOrWhiteSpace(environment))
                    _dbServerReading = environment;

                return _dbServerReading;
            }
        }

        private static string _dbName = null;

        public string DB_NAME
        {
            get
            {
                if (_dbName != null)
                    return _dbName;

                var environment = Environment.GetEnvironmentVariable("DB_NAME_MULTIPAY");
                _dbName = db;

                if (!string.IsNullOrWhiteSpace(environment))
                    _dbName = environment;

                return _dbName;
            }
        }

        private static string _dbUser = null;

        public string DB_USER
        {
            get
            {
                if (_dbUser != null)
                    return _dbUser;

                var environment = Environment.GetEnvironmentVariable("DB_USER_MULTIPAY");
                _dbUser = dbUser;

                if (!string.IsNullOrWhiteSpace(environment))
                    _dbUser = environment;

                return _dbUser;
            }
        }

        private static string _dbPassword = null;

        public string DB_PASSWORD
        {
            get
            {
                if (_dbPassword != null)
                    return _dbPassword;

                var environment = Environment.GetEnvironmentVariable("DB_PASSWORD_MULTIPAY");
                _dbPassword = dbPassword;

                if (!string.IsNullOrWhiteSpace(environment))
                    _dbPassword = environment;

                return _dbPassword;
            }
        }

        public static bool DB_ENCRYPTED
        {
            get { return dbEncripted; }
        }

        public string ConnectionStringDataBase
        {
            get
            {
                var dataSource = isWriting ? DB_SERVER : DB_SERVER_READING;

                var password = dbEncripted
                    ? _criptoService.Decrypt(DB_PASSWORD).Result
                    : DB_PASSWORD;

                return string.Format(
                    "data source={0};TrustServerCertificate=True;initial catalog={1};persist security info=True;user id={2};password=***;MultipleActiveResultSets=True;pooling=false;min pool size={4};max pool size={5};Integrated Security=false;Application Name={6};{7}",
                    dataSource,
                    DB_NAME,
                    DB_USER,
                    password,
                    this.minPoolConnection,
                    this.maxPoolConnection,
                    this.applicationName,
                    ReadingConnectionStringComplement
                );
            }
        }

        public enum ConnectionTypeEnum
        {
            ConnectPSP_Prod_Api,
            ConnectPSP_Prod_Webhook,
            ConnectPSP_Prod_Manager,
            ConnectPSP_Prod_Painel,
            ConnectPSP_Hml_Api,
            ConnectPSP_Hml_Webhook,
            ConnectPSP_Hml_Manager,
            ConnectPSP_Hml_Painel,
        }

        private void SetConnectionInfo()
        {
            switch (CurrentConnectionType)
            {
                case ConnectionTypeEnum.ConnectPSP_Prod_Api:

                    dbServer = @"*************";
                    dbServerReading = @"*************";
                    db = "CONNECTPSP_DB";
                    dbUser = "cpsp_api";
                    dbPassword = "1vbQqSECplbLQiA8teqrAwi/79iMxb/+tiWDRgdSKLY=";
                    applicationName = "API";
                    minPoolConnection = 100;
                    maxPoolConnection = 1000;
                    dbEncripted = true;

                    break;

                case ConnectionTypeEnum.ConnectPSP_Prod_Webhook:

                    dbServer = @"*************";
                    dbServerReading = @"*************";
                    db = "CONNECTPSP_DB";
                    dbUser = "cpsp_webhook";
                    dbPassword = "SPDQOx/cJ8zP4zxYK7FilLkn9wb8jkSEmEScrmGoPQg=";
                    applicationName = "Webhook";
                    minPoolConnection = 100;
                    maxPoolConnection = 1000;
                    dbEncripted = true;

                    break;

                case ConnectionTypeEnum.ConnectPSP_Prod_Manager:

                    dbServer = @"*************";
                    dbServerReading = @"*************";
                    db = "CONNECTPSP_DB";
                    dbUser = "cpsp_manager";
                    dbPassword = "dkTh5h/z/goXUn4YvZy+anXCLyH2Eo0w33aZO/+W1Vk=";
                    applicationName = "Manager";
                    minPoolConnection = 100;
                    maxPoolConnection = 1000;
                    dbEncripted = true;

                    break;

                case ConnectionTypeEnum.ConnectPSP_Prod_Painel:

                    dbServer = @"*************";
                    dbServerReading = @"*************";
                    db = "CONNECTPSP_DB";
                    dbUser = "cpsp_painel";
                    dbPassword = "suAXLiZEgdRmnYkYJA3ezIFDqHmTo8/tkvmjld5M3Kg=";
                    applicationName = "Painel";
                    minPoolConnection = 100;
                    maxPoolConnection = 1000;
                    dbEncripted = true;

                    break;

                case ConnectionTypeEnum.ConnectPSP_Hml_Api:

                    dbServer = @"kestrel.sql.homol.connectpsp.com";
                    dbServerReading = @"kestrel.sql.homol.connectpsp.com";
                    db = "CONNECT_DB";
                    dbUser = "anspace_porhml";
                    dbPassword = "!$s(9ClUxrH-Urn<";
                    applicationName = "API";
                    minPoolConnection = 100;
                    maxPoolConnection = 1000;
                    dbEncripted = false;

                    break;

                case ConnectionTypeEnum.ConnectPSP_Hml_Webhook:

                    dbServer = @"kestrel.sql.homol.connectpsp.com";
                    dbServerReading = @"kestrel.sql.homol.connectpsp.com";
                    db = "CONNECT_DB";
                    dbUser = "anspace_porhml";
                    dbPassword = "!$s(9ClUxrH-Urn<";
                    applicationName = "Webhook";
                    minPoolConnection = 100;
                    maxPoolConnection = 1000;
                    dbEncripted = false;

                    break;

                case ConnectionTypeEnum.ConnectPSP_Hml_Manager:

                    dbServer = @"kestrel.sql.homol.connectpsp.com";
                    dbServerReading = @"kestrel.sql.homol.connectpsp.com";
                    db = "CONNECT_DB";
                    dbUser = "anspace_porhml";
                    dbPassword = "!$s(9ClUxrH-Urn<";
                    applicationName = "Manager";
                    minPoolConnection = 100;
                    maxPoolConnection = 1000;
                    dbEncripted = false;

                    break;

                case ConnectionTypeEnum.ConnectPSP_Hml_Painel:

                    dbServer = @"kestrel.sql.homol.connectpsp.com";
                    dbServerReading = @"kestrel.sql.homol.connectpsp.com";
                    db = "CONNECT_DB";
                    dbUser = "anspace_porhml";
                    dbPassword = "!$s(9ClUxrH-Urn<";
                    applicationName = "Painel";
                    minPoolConnection = 100;
                    maxPoolConnection = 1000;
                    dbEncripted = false;

                    break;

                default:

                    break;
            }
        }
    }
}
