﻿using PigPag.Resources;
using System.Collections.Generic;
using System.Web.Mvc;

namespace PigPag.Common.DropDown
{
    public class DropDownList
    {
        private List<Item> itens;

        public DropDownList()
        {
            this.itens = new List<Item>();
        }

        public DropDownList(List<Item> itens)
        {
            this.itens = new List<Item>();
            this.itens = itens;
        }

        public void Add(object id, string nome)
        {
            if (itens == null)
                itens = new List<Item>();
            itens.Add(new Item() { Id = id, Nome = nome });
        }

        public class Item
        {
            public object Id { get; set; }
            public string Nome { get; set; }
        }

        public List<Item> Itens
        { get { return itens; } set { itens = value; } }

        public SelectList GetDropDownListSimNao()
        {
            this.Add("true", Resource.Sim);
            this.Add("false", Resource.Nao);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListSimNao(bool? padrao)
        {
            this.Add("true", Resource.Sim);
            this.Add("false", Resource.Nao);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListIdiomas()
        {
            this.Add("pt-BR", Resources.PigPag.Common.DropDownListResource.Idiomas_PtBR);
            this.Add("en-US", Resources.PigPag.Common.DropDownListResource.Idiomas_EnUS);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListIdiomas(string padrao)
        {
            this.Add("pt-BR", Resources.PigPag.Common.DropDownListResource.Idiomas_PtBR);
            this.Add("en-US", Resources.PigPag.Common.DropDownListResource.Idiomas_EnUS);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListEstadoCivil()
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Casado);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Divorciado);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Separado);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Solteiro);
            this.Add(5, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Viuvo);
            this.Add(6, "Não Informado");
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListEstadoCivil(object padrao)
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Casado);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Divorciado);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Separado);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Solteiro);
            this.Add(5, Resources.PigPag.Common.DropDownListResource.EstadoCivil_Viuvo);
            this.Add(6, "Não Informado");
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListDiasResgateValor()
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_Imediatamente);
            this.Add(6, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_6Horas);
            this.Add(12, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_12Horas);
            this.Add(24, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_1Dia);
            this.Add(48, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_2Dias);
            this.Add(72, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_3Dias);
            this.Add(120, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_5Dias);
            this.Add(168, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_7Dias);
            this.Add(336, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_14Dias);
            this.Add(720, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_30Dias);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListDiasResgateValor(object padrao)
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_Imediatamente);
            this.Add(6, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_6Horas);
            this.Add(12, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_12Horas);
            this.Add(24, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_1Dia);
            this.Add(48, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_2Dias);
            this.Add(72, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_3Dias);
            this.Add(120, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_5Dias);
            this.Add(168, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_7Dias);
            this.Add(336, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_14Dias);
            this.Add(720, Resources.PigPag.Common.DropDownListResource.DiasResgateValor_30Dias);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListSexo()
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.Sexo_Feminino);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.Sexo_Masculino);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListSexo(object padrao)
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.Sexo_Feminino);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.Sexo_Masculino);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListTipoPessoa()
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.TipoPessoa_Fisica);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.TipoPessoa_Juridica);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListTipoPessoa(object padrao)
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.TipoPessoa_Fisica);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.TipoPessoa_Juridica);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListRegimeTributario()
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.RegimeTributario_SimplesNacional);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.RegimeTributario_LucroReal);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.RegimeTributario_LucroPresumido);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.RegimeTributario_SociedadeAnonima);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListRegimeTributario(object padrao)
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.RegimeTributario_SimplesNacional);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.RegimeTributario_LucroReal);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.RegimeTributario_LucroPresumido);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.RegimeTributario_SociedadeAnonima);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListMoedas()
        {
            this.Add("BTC", "Bitcoin");
            this.Add("BRL", "Real (BR)");
            this.Add("EUR", "Euro");
            this.Add("USD", "Dollar (USA)");
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListMoedas(string padrao)
        {
            this.Add("BTC", "Bitcoin");
            this.Add("BRL", "Real (BR)");
            this.Add("EUR", "Euro");
            this.Add("USD", "Dollar (USA)");
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListNumeroParcelas()
        {
            this.Add("1", $"1 {Resources.PigPag.Common.DropDownListResource.Parcela}");
            this.Add("2", $"2 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("3", $"3 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("4", $"4 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("5", $"5 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("6", $"6 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("7", $"7 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("8", $"8 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("9", $"9 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("10", $"10 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("11", $"11 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("12", $"12 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListNumeroParcelas(object padrao)
        {
            this.Add("1", $"1 {Resources.PigPag.Common.DropDownListResource.Parcela}");
            this.Add("2", $"2 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("3", $"3 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("4", $"4 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("5", $"5 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("6", $"6 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("7", $"7 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("8", $"8 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("9", $"9 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("10", $"10 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("11", $"11 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("12", $"12 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListNumeroParcelasAcima1()
        {
            this.Add("2", $"2 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("3", $"3 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("4", $"4 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("5", $"5 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("6", $"6 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("7", $"7 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("8", $"8 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("9", $"9 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("10", $"10 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("11", $"11 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("12", $"12 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListNumeroParcelasAcima1(object padrao)
        {
            this.Add("2", $"2 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("3", $"3 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("4", $"4 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("5", $"5 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("6", $"6 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("7", $"7 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("8", $"8 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("9", $"9 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("10", $"10 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("11", $"11 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            this.Add("12", $"12 {Resources.PigPag.Common.DropDownListResource.Parcelas}");
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListUF()
        {
            this.Add("AC", "Acre");
            this.Add("AL", "Alagoas");
            this.Add("AP", "Amapá");
            this.Add("AM", "Amazonas");
            this.Add("BA", "Bahia");
            this.Add("CE", "Ceará");
            this.Add("DF", "Distrito Federal");
            this.Add("ES", "Espírito Santo");
            this.Add("GO", "Goiás");
            this.Add("MA", "Maranhão");
            this.Add("MT", "Mato Grosso");
            this.Add("MS", "Mato Grosso do Sul");
            this.Add("MG", "Minas Gerais");
            this.Add("PA", "Pará");
            this.Add("PB", "Paraíba");
            this.Add("PR", "Paraná");
            this.Add("PE", "Pernambuco");
            this.Add("PI", "Piauí");
            this.Add("RJ", "Rio de Janeiro");
            this.Add("RN", "Rio Grande do Norte");
            this.Add("RS", "Rio Grande do Sul");
            this.Add("RO", "Rondônia");
            this.Add("RR", "Roraima");
            this.Add("SC", "Santa Catarina");
            this.Add("SP", "São Paulo");
            this.Add("SE", "Sergipe");
            this.Add("TO", "Tocantins");
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListUF(object padrao)
        {
            this.Add("AC", "Acre");
            this.Add("AL", "Alagoas");
            this.Add("AP", "Amapá");
            this.Add("AM", "Amazonas");
            this.Add("BA", "Bahia");
            this.Add("CE", "Ceará");
            this.Add("DF", "Distrito Federal");
            this.Add("ES", "Espírito Santo");
            this.Add("GO", "Goiás");
            this.Add("MA", "Maranhão");
            this.Add("MT", "Mato Grosso");
            this.Add("MS", "Mato Grosso do Sul");
            this.Add("MG", "Minas Gerais");
            this.Add("PA", "Pará");
            this.Add("PB", "Paraíba");
            this.Add("PR", "Paraná");
            this.Add("PE", "Pernambuco");
            this.Add("PI", "Piauí");
            this.Add("RJ", "Rio de Janeiro");
            this.Add("RN", "Rio Grande do Norte");
            this.Add("RS", "Rio Grande do Sul");
            this.Add("RO", "Rondônia");
            this.Add("RR", "Roraima");
            this.Add("SC", "Santa Catarina");
            this.Add("SP", "São Paulo");
            this.Add("SE", "Sergipe");
            this.Add("TO", "Tocantins");
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListTipoContaBancaria()
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.TipoContaBancaria_ContaCorrente);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.TipoContaBancaria_ContaPoupanca);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.TipoContaBancaria_ContaPagamento);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListTipoContaBancaria(object padrao)
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.TipoContaBancaria_ContaCorrente);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.TipoContaBancaria_ContaPoupanca);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.TipoContaBancaria_ContaPagamento);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListTipoChavePIX()
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.TipoChavePIX_CPFCNPJ);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.TipoChavePIX_Celular);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.TipoChavePIX_Email);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.TipoChavePIX_Aleatoria);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListTipoChavePIX(object padrao)
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.TipoChavePIX_CPFCNPJ);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.TipoChavePIX_Celular);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.TipoChavePIX_Email);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.TipoChavePIX_Aleatoria);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListFormaPagamento()
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.Todas);
            this.Add(5, Resources.PigPag.Common.DropDownListResource.FormaPagamento_Bitcoin);
            this.Add(1, Resources.PigPag.Common.DropDownListResource.FormaPagamento_BoletoBancario);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.FormaPagamento_CartaoCredito_AVista);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.FormaPagamento_CartaoCredito_Parcelado);
            this.Add(6, Resources.PigPag.Common.DropDownListResource.FormaPagamento_CartaoDebito);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.FormaPagamento_DebitoOnline);
            this.Add(7, Resources.PigPag.Common.DropDownListResource.FormaPagamento_TransferenciaInterna);
            this.Add(8, Resources.PigPag.Common.DropDownListResource.FormaPagamento_PIX);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListStatus()
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.Todos);
            this.Add(1, Resources.PigPag.Common.DropDownListResource.StatusCobranca_AguardandoPagamento);           
            this.Add(2, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Vencido);           
            this.Add(3, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Cancelado);           
            this.Add(4, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Pago);           
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListBancos()
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.Todos);
            //Alterar para buscar no banco de dados os bancos ativos

            //this.Add(137, Resources.PigPag.Common.DropDownListResource.Bancos_Genial);
            //this.Add(238, Resources.PigPag.Common.DropDownListResource.Bancos_Aarin);
            this.Add(232, Resources.PigPag.Common.DropDownListResource.Bancos_Delbank);
            //this.Add(115, Resources.PigPag.Common.DropDownListResource.Bancos_BS2);
            //this.Add(229, Resources.PigPag.Common.DropDownListResource.Bancos_Sicoob);
            this.Add(231, "CELCOIN");
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListFormaPagamento(object padrao)
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.Todas);
            this.Add(5, Resources.PigPag.Common.DropDownListResource.FormaPagamento_Bitcoin);
            this.Add(1, Resources.PigPag.Common.DropDownListResource.FormaPagamento_BoletoBancario);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.FormaPagamento_CartaoCredito_AVista);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.FormaPagamento_CartaoCredito_Parcelado);
            this.Add(6, Resources.PigPag.Common.DropDownListResource.FormaPagamento_CartaoDebito);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.FormaPagamento_DebitoOnline);
            this.Add(7, Resources.PigPag.Common.DropDownListResource.FormaPagamento_TransferenciaInterna);
            this.Add(8, Resources.PigPag.Common.DropDownListResource.FormaPagamento_PIX);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListTempoLimiteCheckout()
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.TempoLimiteCheckout_Ilimitado);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.TempoLimiteCheckout_3Minutos);
            this.Add(5, Resources.PigPag.Common.DropDownListResource.TempoLimiteCheckout_5Minutos);
            this.Add(10, Resources.PigPag.Common.DropDownListResource.TempoLimiteCheckout_10Minutos);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListTempoLimiteCheckout(object padrao)
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.TempoLimiteCheckout_Ilimitado);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.TempoLimiteCheckout_3Minutos);
            this.Add(5, Resources.PigPag.Common.DropDownListResource.TempoLimiteCheckout_5Minutos);
            this.Add(10, Resources.PigPag.Common.DropDownListResource.TempoLimiteCheckout_10Minutos);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListStatusCobranca()
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.Todos);
            this.Add(1, Resources.PigPag.Common.DropDownListResource.StatusCobranca_EmAberto);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Paga);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Vencida);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Cancelada);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListStatusCobranca(object padrao)
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.Todos);
            this.Add(1, Resources.PigPag.Common.DropDownListResource.StatusCobranca_EmAberto);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Paga);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Vencida);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.StatusCobranca_Cancelada);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListTipoCliente()
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.TipoCliente_Varejo);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.TipoCliente_Representante);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.TipoCliente_GrupoCorporativo);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.TipoCliente_Consumidor);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListTipoCliente(object padrao)
        {
            this.Add(1, Resources.PigPag.Common.DropDownListResource.TipoCliente_Varejo);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.TipoCliente_Representante);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.TipoCliente_GrupoCorporativo);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.TipoCliente_Consumidor);
            return new SelectList(itens, "Id", "Nome", padrao);
        }

        public SelectList GetDropDownListStatusSolicitacaoSaque()
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.Todos);
            this.Add(1, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_EmAnalise);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Processado);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Confirmado);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Recusado);
            this.Add(5, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Cancelado);
            this.Add(6, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Estornado);
            this.Add(7, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_PagamentoIniciado);
            this.Add(8, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_ProcessoInterrompido);
            return new SelectList(itens, "Id", "Nome");
        }

        public SelectList GetDropDownListStatusSolicitacaoSaque(object padrao)
        {
            this.Add(0, Resources.PigPag.Common.DropDownListResource.Todos);
            this.Add(1, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_EmAnalise);
            this.Add(2, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Processado);
            this.Add(3, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Confirmado);
            this.Add(4, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Recusado);
            this.Add(5, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Cancelado);
            this.Add(6, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_Estornado);
            this.Add(7, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_PagamentoIniciado);
            this.Add(8, Resources.PigPag.Common.DropDownListResource.StatusSolicitacaoSaque_ProcessoInterrompido);
            return new SelectList(itens, "Id", "Nome", padrao);
        }
    }
}