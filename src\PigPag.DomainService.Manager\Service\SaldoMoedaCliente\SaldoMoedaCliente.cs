﻿using PigPag.DataModel.SaldoMoedaCliente;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading;
using System.Threading.Tasks;

namespace PigPag.DomainService.Manager.Service
{
    public class SaldoMoedaCliente
    {
        public async Task<decimal> SelectSaldoMoedaClienteSomatorioMoeda(byte idMoeda, int? idOperador, CancellationToken cancellationToken)
        {
            try
            {
                using (var conn = new SqlConnection(new Connection.SQLServerConnection("MultiPay Manager", 10, 20, false).ConnectionStringDataBase))
                {
                    using (var command = new SqlCommand("Manager.Select_SaldoMoedaClienteSomatorioMoeda", conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new SqlParameter("@idMoeda", idMoeda));
                        command.Parameters.Add(new SqlParameter("@idOperador", idOperador));
                        await conn.OpenAsync(cancellationToken);
                        return (decimal)(await command.ExecuteScalarAsync(cancellationToken));
                    }
                }
            }
            catch (Exception) { throw; }
        }

        public virtual async Task<IList<SelectSaldoMoedaClienteReturnModel>> SelectSaldoMoedaCliente(byte idMoeda, decimal saldoMinimo, int? idOperador)
        {
            try
            {
                List<SelectSaldoMoedaClienteReturnModel> retorno = null;
                using (var conn = new SqlConnection(new Connection.SQLServerConnection("MultiPay Manager", 10, 20, false).ConnectionStringDataBase))
                using (var command = new SqlCommand("Manager.Select_SaldoMoedaCliente", conn))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@idMoeda", idMoeda));
                    command.Parameters.Add(new SqlParameter("@saldoMinimo", saldoMinimo));
                    command.Parameters.Add(new SqlParameter("@idoperador", idOperador));
                    conn.Open();
                    using (var r = await command.ExecuteReaderAsync())
                    {
                        if (r.HasRows)
                            retorno = new List<SelectSaldoMoedaClienteReturnModel>();
                        while (r.Read())
                        {
                            retorno.Add(new SelectSaldoMoedaClienteReturnModel()
                            {
                                IdCliente = !r.IsDBNull(r.GetOrdinal("IdCliente")) ? r.GetInt32(r.GetOrdinal("IdCliente")) : 0,
                                IdContaBancariaEmpresa = !r.IsDBNull(r.GetOrdinal("IdContaBancariaEmpresa")) ? r.GetInt32(r.GetOrdinal("IdContaBancariaEmpresa")) : 0,
                                CodigoCliente = !r.IsDBNull(r.GetOrdinal("CodigoCliente")) ? r.GetString(r.GetOrdinal("CodigoCliente")) : null,
                                Nome = !r.IsDBNull(r.GetOrdinal("Nome")) ? r.GetString(r.GetOrdinal("Nome")) : null,
                                NomeFantasia = !r.IsDBNull(r.GetOrdinal("NomeFantasia")) ? r.GetString(r.GetOrdinal("NomeFantasia")) : null,
                                RazaoSocial = !r.IsDBNull(r.GetOrdinal("RazaoSocial")) ? r.GetString(r.GetOrdinal("RazaoSocial")) : null,
                                Saldo = !r.IsDBNull(r.GetOrdinal("Saldo")) ? r.GetDecimal(r.GetOrdinal("Saldo")) : 0.00M,
                                NomeOperador = !r.IsDBNull(r.GetOrdinal("NomeOperador")) ? r.GetString(r.GetOrdinal("NomeOperador")) : null,
                                Banco = !r.IsDBNull(r.GetOrdinal("Banco")) ? r.GetString(r.GetOrdinal("Banco")) : null,
                                Conta = !r.IsDBNull(r.GetOrdinal("Conta")) ? r.GetString(r.GetOrdinal("Conta")) : null,
                                TipoContaEmpresa = !r.IsDBNull(r.GetOrdinal("TipoContaEmpresa")) ? r.GetString(r.GetOrdinal("TipoContaEmpresa")) : null,
                                TarifasPendentes = !r.IsDBNull(r.GetOrdinal("TarifasPendentes")) ? r.GetDecimal(r.GetOrdinal("TarifasPendentes")) : 0.00M,
                            });
                        }
                        return retorno;
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}