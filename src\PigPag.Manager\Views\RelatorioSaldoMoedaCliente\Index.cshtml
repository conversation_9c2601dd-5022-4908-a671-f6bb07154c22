﻿<link href="~/plugins/datatables/PigShop/css/dataTables.bootstrap.min.css" rel="stylesheet" />
<link href="~/plugins/datatables/PigShop/buttons.bootstrap.min.css" rel="stylesheet" />
<script src="~/plugins/datatables/PigShop/js/jquery.dataTables.min.js"></script>
<script src="~/plugins/datatables/PigShop/js/dataTables.bootstrap4.min.js"></script>
<script src="~/plugins/datatables/PigShop/dataTables.buttons.min.js"></script>
<script src="~/plugins/datatables/PigShop/buttons.bootstrap.min.js"></script>
<script src="~/plugins/datatables/buttons.colVis.min.js"></script>
<script src="~/plugins/datatables/buttons.html5.min.js"></script>
<script src="~/plugins/datatables/buttons.print.min.js"></script>
<script src="~/plugins/datatables/jszip.min.js"></script>
<script src="~/plugins/datatables/pdfmake.min.js"></script>
<script src="~/plugins/datatables/vfs_fonts.js"></script>
<script src="~/plugins/datatables/PigShop/moment-with-locales-2.22.2.min.js"></script>

<style>
    .dt-buttons {
        float: right;
    }
</style>

<section class="content-header">
    <h1>
        Saldos dos Clientes
    </h1>
</section>


<section class="content">
    <div class="row">
        <div class="col-xs-12">
            <div class="box box-success">
                <div class="box-body padding">
                    <div class="row" id="conteudo">
                        <div class="col-lg-12">
                            @Html.Partial("Table", new DataTablesModel
                       {
                           Info = true,
                           LengthMenu = "[10, 25, 50, -1], [10, 25, 50, 'Todos']",
                           Name = "listar-saldos-clientes",
                           UrlRead = new DataUrl("ListarSaldoMoedaCliente", "RelatorioSaldoMoedaCliente", null),
                           Paging = true,
                           Ordering = true,
                           Processing = true,
                           ServerSide = false,
                           PrimaryKeyColumn = "IdCliente",
                           OrderColumn = "[1, 'asc'], [2, 'desc'], [4, 'desc']",
                           Length = 50,
                           Dom = "<'row'<'col-md-12't>>" +
                            "<'row margin-t-5'" +
                            "<'col-lg-10 col-xs-12'<'float-lg-left'p>>" +
                            "<'col-lg-2 col-xs-12'<'float-lg-right text-center'i>>" +
                            ">",
                           ColumnCollection = new List<ColumnProperty>
                            {
                               //new ColumnProperty("NomeOperador")
                               // {
                               // Title = "Operador",
                               // Width = "40",
                               //  Searchable = true,
                               //   AutoWidth = true,
                               // },
                               //new ColumnProperty("CodigoCliente")
                               // {
                               // Title = "Código",
                               // Width = "40",
                               //  Searchable = true,
                               //   AutoWidth = true,
                               // },
                                new ColumnProperty("RazaoSocial")
                                {
                                Title = "Razão Social",
                                Width = "250",
                                 Searchable = true,
                                  AutoWidth = true,
                                },
                                new ColumnProperty("NomeFantasia")
                                {
                                Title = "Nome Fantasia",
                                Width = "100",
                                 Searchable = true,
                                  AutoWidth = true
                                },
                                new ColumnProperty("Banco")
                                {
                                Title = "Banco",
                                Width = "50",
                                 Searchable = true,
                                  AutoWidth = true,
                                },
                                new ColumnProperty("Conta")
                                {
                                Title = "Conta",
                                Width = "50",
                                 Searchable = true,
                                  AutoWidth = true,
                                },
                                new ColumnProperty("TipoContaEmpresa")
                                {
                                Title = "Tipo",
                                Width = "60",
                                    Searchable = true,
                                    AutoWidth = true,
                                },
                                new ColumnProperty("Saldo")
                                {
                                    Title = "Saldo na Connect",
                                    Width = "60",
                                    Searchable = true,
                                    AutoWidth = true,
                                    Render = new RenderCurrency("R$"),
                                },
                                new ColumnProperty("SaldoBanco")
                                {
                                    Title = "Saldo no Banco",
                                    Width = "60",
                                    Searchable = true,
                                    AutoWidth = true,
                                    Render = new RenderCurrency("R$"),
                                },
                                new ColumnProperty("TarifasPendentes")
                                {
                                    Title = "Tarifas Pendentes",
                                    Width = "60",
                                    Searchable = true,
                                    AutoWidth = true,
                                    Render = new RenderCurrency("R$"),
                                }
                                }
                       })
                            <script>

                                function renderColumnCustomerEmail(data, type, row, meta) {
                                    var textRenderer = $.fn.dataTable.render.text().display;
                                    return textRenderer(row.CustomerFullName) + ' (' + textRenderer(row.CustomerEmail) + ')';
                                }

                                function renderColumnStatus(data, type, row, meta) {
                                    var color;
                                    if (row.DataConclusao == null) {
                                        color = 'bg-yellow';
                                        data = "AGUARDANDO ANÁLISE";
                                    }
                                    else {
                                        color = 'bg-green';
                                        data = "CONCLUÍDO";
                                    }
                                    return '<span class="badge ' + color + '">' + data + '</span >';
                                }

                                function aplicarEstilo() {
                                    $('td').filter(function () {
                                        return $(this).text().includes('R$');
                                    }).css('text-align', 'right');
                                    $('th').filter(function () {
                                        return $(this).text().includes('Saldo');
                                    }).css('text-align', 'right');
                                }

                                // Configura o MutationObserver para observar alterações no DOM
                                const observer = new MutationObserver(function (mutationsList) {
                                    aplicarEstilo();
                                });

                                // Observa mudanças no corpo da tabela
                                observer.observe(document.body, { childList: true, subtree: true });

                            </script>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>